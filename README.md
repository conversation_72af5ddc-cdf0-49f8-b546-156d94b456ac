# FlashBookkeeping（闪电记账）

<p align="center">
  <img src="https://img.shields.io/badge/Node.js-18+-green.svg" alt="Node.js" />
  <img src="https://img.shields.io/badge/TypeScript-5.0+-blue.svg" alt="TypeScript" />
  <img src="https://img.shields.io/badge/NestJS-10.0+-red.svg" alt="NestJS" />
  <img src="https://img.shields.io/badge/PostgreSQL-13+-blue.svg" alt="PostgreSQL" />
  <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License" />
</p>

<p align="center">
  一个基于企业微信的智能记账应用，通过AI技术自动识别和处理用户的记账信息，并将数据同步到Notion第三方平台。
</p>

## ✨ 特性

- 🤖 **AI智能识别**: 使用多模态大语言模型从文本和图片中提取账单信息
- 💬 **企业微信客服消息**: 完整的客服消息处理流程，支持URL验证和消息回调
- 🔄 **多平台同步**: 支持同步到Notion平台，使用OAuth授权流程
- 📊 **订阅管理**: 内置订阅和用量跟踪系统
- 🔒 **安全数据处理**: 敏感信息加密存储，完整的签名验证
- 🚀 **高性能**: 基于NestJS构建，支持集群部署
- 📱 **多模态支持**: 支持文本、图片、语音、文件等多种消息类型
- 🔧 **增量消息拉取**: 使用Token和Cursor机制拉取客服消息

## 🛠️ 技术栈

- **后端框架**: NestJS (TypeScript)
- **数据库**: PostgreSQL + Prisma ORM
- **AI服务**: 豆包LLM (Doubao)
- **第三方API**: Notion API, 企业微信API
- **包管理器**: pnpm
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2

## 🚀 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 13+
- RabbitMQ 3.8+ (消息队列)
- pnpm 8+

### 安装步骤

1. **克隆仓库**
```bash
git clone <repository-url>
cd FlashBookkeeping
```

2. **安装依赖**
```bash
pnpm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

4. **运行数据库迁移**
```bash
pnpm prisma migrate dev
```

5. **启动开发服务器**
```bash
pnpm run start:dev
```

6. **访问应用**
- 应用地址: http://localhost:3000
- API文档: http://localhost:3000/api
- 健康检查: http://localhost:3000/health

### Docker 部署

#### 生产环境
```bash
# 启动所有服务 (PostgreSQL + RabbitMQ + App + Nginx)
docker-compose up -d

# 运行数据库迁移
docker-compose exec app pnpm prisma migrate deploy

# 查看服务状态
docker-compose ps
```

#### 开发环境
```bash
# 仅启动基础服务 (PostgreSQL + RabbitMQ + Redis)
docker-compose -f docker-compose.dev.yml up -d

# 本地运行应用
pnpm run start:dev
```

#### 访问服务
- 应用: http://localhost:3000
- RabbitMQ管理界面: http://localhost:15672 (admin/flashrabbit)
- API文档: http://localhost:3000/api

## 📚 文档

- [API文档](docs/api.md) - 详细的API接口文档
- [企业微信消息流程](docs/wecom-kf-message-flow.md) - 客服消息处理完整流程
- [Notion集成设置](docs/notion-setup.md) - Notion OAuth集成配置指南
- [部署指南](docs/deployment.md) - 生产环境部署指南
- [用户指南](docs/user-guide.md) - 用户使用指南
- [架构设计](docs/architecture.md) - 系统架构设计
- [数据库设计](docs/database.md) - 数据库模型设计

## 🔧 开发

### 可用脚本

```bash
# 开发
pnpm run start:dev          # 启动开发服务器
pnpm run start:debug        # 启动调试模式

# 构建
pnpm run build              # 构建生产版本
pnpm run start:prod         # 启动生产服务器

# 测试
pnpm run test               # 运行单元测试
pnpm run test:e2e           # 运行端到端测试
pnpm run test:cov           # 运行测试覆盖率

# 数据库
pnpm prisma migrate dev     # 运行开发迁移
pnpm prisma migrate deploy  # 运行生产迁移
pnpm prisma generate        # 生成Prisma客户端
pnpm prisma studio          # 打开数据库管理界面

# 代码质量
pnpm run lint               # 代码检查
pnpm run format             # 代码格式化
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [NestJS](https://nestjs.com/) - 强大的Node.js框架
- [Prisma](https://www.prisma.io/) - 现代数据库工具包
- [Notion API](https://developers.notion.com/) - Notion集成支持
- [企业微信API](https://developer.work.weixin.qq.com/) - 企业微信集成支持