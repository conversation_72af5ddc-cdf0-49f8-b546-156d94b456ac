// 创建测试数据的脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('🔧 创建测试数据...');
    
    // 创建测试用户
    const user = await prisma.user.upsert({
      where: { id: 'test_bound_user_456' },
      update: {},
      create: {
        id: 'test_bound_user_456',
        name: '测试已绑定用户',
      },
    });
    
    console.log('✅ 创建测试用户:', user.name);
    
    // 创建测试绑定记录
    const binding = await prisma.binding.upsert({
      where: { id: 'test_binding_123' },
      update: {
        access_token: '8f0169e2516d3ce5adb663b875eebaa2.383e5580fe595747968e7c2504da073e3d28ce8469b05809cdda96c0cc858a8a',
      },
      create: {
        id: 'test_binding_123',
        user_id: 'test_bound_user_456',
        type: 'NOTION',
        access_token: '8f0169e2516d3ce5adb663b875eebaa2.383e5580fe595747968e7c2504da073e3d28ce8469b05809cdda96c0cc858a8a',
        target_id: 'test_database_id_789',
        target_name: '测试记账数据库',
        is_active: true,
      },
    });
    
    console.log('✅ 创建测试绑定记录:', binding.target_name);
    console.log('🎉 测试数据创建完成！');
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestData();
