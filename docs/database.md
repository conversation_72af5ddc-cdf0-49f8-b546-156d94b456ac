# 1. 数据库设计

本文档定义了“闪电记账”项目所使用的 PostgreSQL 数据库的核心表结构和关系。

## 1.1. ER 关系图

```mermaid
erDiagram
    users {
        varchar(255) id PK "企业微信 ExternalUserID"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    bindings {
        varchar(36) id PK "UUID"
        varchar(255) user_id FK "关联用户ID"
        varchar(50) type "绑定类型 (notion)"
        text access_token "加密存储的 token"
        varchar(255) target_id "Notion DB ID"
        varchar(255) target_name "数据库或表格名称"
        boolean is_active "是否为当前活动绑定"
        timestamp created_at "创建时间"
    }

    subscriptions {
        varchar(36) id PK "UUID"
        varchar(255) user_id FK "关联用户ID"
        varchar(50) plan "订阅套餐 (monthly, yearly)"
        timestamp start_date "订阅开始日期"
        timestamp end_date "订阅结束日期"
        varchar(50) status "状态 (active, canceled, expired)"
        timestamp created_at "创建时间"
    }

    usage_stats {
        varchar(36) id PK "UUID"
        varchar(255) user_id FK "关联用户ID"
        integer year_month "统计年月 (例如 202307)"
        integer count "当月已用条目数"
        timestamp updated_at "最后更新时间"
    }

    wecom_sync_cursors {
        varchar(255) id PK "唯一标识，可为'global_cursor'"
        varchar(255) cursor "上次同步的游标"
        timestamp updated_at "更新时间"
    }

    users ||--o{ bindings : "拥有"
    users ||--o{ subscriptions : "拥有"
    users ||--o{ usage_stats : "拥有"
```

## 1.2. 表结构详情

### 1.2.1. 用户表 (`users`)
存储通过企业微信接入的用户基本信息。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `VARCHAR(255)` | `PRIMARY KEY` | 用户的唯一标识，直接使用企业微信的 `ExternalUserID`。 |
| `created_at` | `TIMESTAMP` | `NOT NULL` | 记录创建时间。 |
| `updated_at` | `TIMESTAMP` | `NOT NULL` | 记录最后更新时间。 |

### 1.2.2. 绑定关系表 (`bindings`)
存储用户与 Notion等第三方应用的绑定信息。一个用户可以有多个绑定关系。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `VARCHAR(36)` | `PRIMARY KEY` | 记录的唯一 UUID。 |
| `user_id` | `VARCHAR(255)` | `FOREIGN KEY (users.id)` | 关联的用户 ID。 |
| `type` | `VARCHAR(50)` | `NOT NULL` | 绑定类型，枚举值：`notion`, `feishu`。 |
| `access_token` | `TEXT` | `NOT NULL` | 加密存储的第三方应用 `access_token`。 |
| `target_id` | `VARCHAR(255)` | `NOT NULL` | 目标的唯一 ID，如 Notion 的 `database_id`。 |
| `target_name` | `VARCHAR(255)` | | 目标数据库或表格的名称，方便前端展示。 |
| `is_active` | `BOOLEAN` | `DEFAULT false` | 标记此绑定是否为当前记账的目标。 |
| `created_at` | `TIMESTAMP` | `NOT NULL` | 记录创建时间。 |

### 1.2.3. 订阅表 (`subscriptions`)
管理用户的付费订阅状态。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `VARCHAR(36)` | `PRIMARY KEY` | 记录的唯一 UUID。 |
| `user_id` | `VARCHAR(255)` | `FOREIGN KEY (users.id)` | 关联的用户 ID。 |
| `plan` | `VARCHAR(50)` | `NOT NULL` | 订阅的套餐类型，如 `monthly`, `yearly`。 |
| `start_date` | `TIMESTAMP` | `NOT NULL` | 订阅周期的开始时间。 |
| `end_date` | `TIMESTAMP` | `NOT NULL` | 订阅周期的结束时间。 |
| `status` | `VARCHAR(50)` | `NOT NULL` | 订阅状态，枚举值：`active`, `canceled`, `expired`。 |
| `created_at` | `TIMESTAMP` | `NOT NULL` | 记录创建时间。 |

### 1.2.4. 用量统计表 (`usage_stats`)
跟踪免费套餐用户的每月记账条目使用情况。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `VARCHAR(36)` | `PRIMARY KEY` | 记录的唯一 UUID。 |
| `user_id` | `VARCHAR(255)` | `FOREIGN KEY (users.id)` | 关联的用户 ID。 |
| `year_month` | `INTEGER` | `NOT NULL` | 统计的年月，格式为 `YYYYMM`，如 `202307`。 |
| `count` | `INTEGER` | `NOT NULL, DEFAULT 0` | 用户在该月已使用的记账条目数。 |
| `updated_at` | `TIMESTAMP` | `NOT NULL` | 记录最后更新时间。 |

### 1.2.5. 企微同步游标表 (`wecom_sync_cursors`)
存储拉取企业微信客服消息的游标，确保增量同步。

| 字段名 | 数据类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `VARCHAR(255)` | `PRIMARY KEY` | 游标的唯一标识。由于通常只有一个全局游标，可以设为固定值如 `global_cursor`。 |
| `cursor` | `VARCHAR(255)` | `NOT NULL` | 企业微信返回的 `next_cursor`，用于下一次拉取。 |
| `updated_at` | `TIMESTAMP` | `NOT NULL` | 记录游标的最后更新时间。 |

**索引建议**:
- 在 `bindings(user_id)` 上创建索引，以快速查询用户的绑定列表。
- 在 `subscriptions(user_id)` 上创建索引，以快速查询用户的订阅状态。
- 在 `usage_stats(user_id, year_month)` 上创建复合唯一索引，确保每个用户每月只有一条用量记录。