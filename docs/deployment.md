# FlashBookkeeping 部署指南

## 概述

本文档介绍如何部署FlashBookkeeping应用到生产环境。

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Node.js**: 18.x 或更高版本
- **pnpm**: 8.x 或更高版本
- **PostgreSQL**: 13.x 或更高版本
- **Redis**: 6.x 或更高版本 (可选，用于消息队列)

### 硬件要求
- **CPU**: 2核心或更多
- **内存**: 4GB 或更多
- **存储**: 20GB 或更多

## 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 准备环境文件

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑环境配置
nano .env
```

#### 2. 配置环境变量

```bash
# 数据库配置
DATABASE_URL="**********************************************/flashbookkeeping?schema=public"

# 企业微信配置
WECOM_CORP_ID="your_corp_id"
WECOM_TOKEN="your_token"
WECOM_ENCODING_AES_KEY="your_encoding_aes_key"
WECOM_SECRET="your_secret"

# 豆包LLM配置
DOUBAO_API_KEY="your_doubao_api_key"
DOUBAO_API_ENDPOINT="https://ark.cn-beijing.volces.com/api/v3"

# Notion配置
NOTION_CLIENT_ID="your_notion_client_id"
NOTION_CLIENT_SECRET="your_notion_client_secret"
NOTION_REDIRECT_URI="https://yourdomain.com/api/notion/callback"

# 应用配置
PORT=3000
NODE_ENV="production"

# JWT配置
JWT_SECRET="your_jwt_secret_key"

# 加密配置
ENCRYPTION_KEY="32_character_long_encryption_key_here"

# Redis配置
REDIS_HOST="redis"
REDIS_PORT=6379

# 日志配置
LOG_LEVEL="info"
LOG_FILE_PATH="./logs"

# 免费用户限制
FREE_USER_MONTHLY_LIMIT=30
```

#### 3. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 4. 数据库迁移

```bash
# 运行数据库迁移
docker-compose exec app pnpm prisma migrate deploy

# 生成Prisma客户端
docker-compose exec app pnpm prisma generate
```

### 方式二：传统部署

#### 1. 安装依赖

```bash
# 安装Node.js和pnpm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g pnpm

# 安装PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# 安装Redis (可选)
sudo apt-get install redis-server
```

#### 2. 配置数据库

```bash
# 创建数据库用户和数据库
sudo -u postgres psql
CREATE USER flashuser WITH PASSWORD 'flashpass';
CREATE DATABASE flashbookkeeping OWNER flashuser;
GRANT ALL PRIVILEGES ON DATABASE flashbookkeeping TO flashuser;
\q
```

#### 3. 部署应用

```bash
# 克隆代码
git clone <repository-url>
cd FlashBookkeeping

# 安装依赖
pnpm install

# 配置环境变量
cp .env.example .env
nano .env

# 运行数据库迁移
pnpm prisma migrate deploy

# 构建应用
pnpm run build

# 启动应用
pnpm run start:prod
```

#### 4. 配置进程管理器

使用PM2管理Node.js进程：

```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'flashbookkeeping',
    script: 'dist/main.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

## 反向代理配置

### Nginx 配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 限流
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    location / {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /health {
        proxy_pass http://localhost:3000;
        access_log off;
    }
}
```

## 监控和日志

### 1. 应用监控

```bash
# 查看PM2状态
pm2 status

# 查看应用日志
pm2 logs flashbookkeeping

# 监控资源使用
pm2 monit
```

### 2. 数据库监控

```bash
# 连接数据库
psql -h localhost -U flashuser -d flashbookkeeping

# 查看连接数
SELECT count(*) FROM pg_stat_activity;

# 查看数据库大小
SELECT pg_size_pretty(pg_database_size('flashbookkeeping'));
```

### 3. 日志管理

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/flashbookkeeping

/path/to/FlashBookkeeping/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload flashbookkeeping
    endscript
}
```

## 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
cat > backup.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/flashbookkeeping"
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump -h localhost -U flashuser flashbookkeeping > $BACKUP_DIR/db_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x backup.sh

# 添加到crontab
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

### 2. 应用备份

```bash
# 备份应用代码和配置
tar -czf flashbookkeeping_$(date +%Y%m%d).tar.gz \
    --exclude=node_modules \
    --exclude=dist \
    --exclude=logs \
    /path/to/FlashBookkeeping
```

## 安全配置

### 1. 防火墙设置

```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw deny 3000  # 只允许通过Nginx访问
```

### 2. SSL证书

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 3. 环境变量安全

- 使用强密码和随机密钥
- 定期轮换API密钥
- 限制数据库用户权限
- 使用环境变量而非硬编码敏感信息

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX idx_user_wecom_id ON "User"(wecom_user_id);
CREATE INDEX idx_binding_user_type ON "Binding"(user_id, type);
CREATE INDEX idx_usage_user_month ON "UsageStat"(user_id, year_month);

-- 配置连接池
-- 在DATABASE_URL中添加连接池参数
-- ?connection_limit=20&pool_timeout=20
```

### 2. 应用优化

```bash
# 启用集群模式
pm2 start ecosystem.config.js --instances max

# 配置缓存
# 在应用中使用Redis缓存频繁查询的数据
```

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查环境变量配置
   - 验证数据库连接
   - 查看应用日志

2. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接字符串
   - 检查防火墙设置

3. **API响应慢**
   - 检查数据库查询性能
   - 监控服务器资源使用
   - 优化数据库索引

### 日志查看

```bash
# 应用日志
pm2 logs flashbookkeeping

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 系统日志
sudo journalctl -u postgresql
sudo journalctl -u redis
```

## 更新部署

### Docker方式更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose down
docker-compose up -d --build

# 运行数据库迁移
docker-compose exec app pnpm prisma migrate deploy
```

### 传统方式更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
pnpm install

# 运行数据库迁移
pnpm prisma migrate deploy

# 重新构建
pnpm run build

# 重启应用
pm2 reload flashbookkeeping
```

## 扩展部署

### 负载均衡

当应用需要处理更多请求时，可以部署多个实例：

```bash
# 启动多个应用实例
pm2 start ecosystem.config.js --instances 4

# 或使用Docker Swarm
docker swarm init
docker stack deploy -c docker-compose.yml flashbookkeeping
```

### 数据库集群

对于高可用性需求，可以配置PostgreSQL主从复制：

```bash
# 主数据库配置
# postgresql.conf
wal_level = replica
max_wal_senders = 3
wal_keep_segments = 64

# pg_hba.conf
host replication replicator slave_ip/32 md5
```
