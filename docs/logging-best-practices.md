# 📝 日志记录最佳实践

## 🎯 日志策略概述

FlashBookkeeping 项目采用分层日志策略，确保关键业务操作、系统错误和安全事件都能被妥善记录和追踪。

## 📊 日志分类

### 1. **控制台日志**
- 开发环境：彩色格式，便于调试
- 生产环境：JSON 格式，便于日志收集

### 2. **文件日志**
- **应用日志** (`application-YYYY-MM-DD.log`): 所有级别的日志
- **错误日志** (`error-YYYY-MM-DD.log`): 错误和异常
- **业务日志** (`business-YYYY-MM-DD.log`): 关键业务操作
- **安全日志** (`security-YYYY-MM-DD.log`): 安全相关事件

## 🔧 配置说明

### 环境变量
```env
# 日志配置
LOG_LEVEL="info"           # 日志级别: debug, info, warn, error
LOG_FILE_PATH="./logs"     # 日志文件路径
LOG_MAX_SIZE="20m"         # 单个日志文件最大大小
LOG_MAX_FILES="30d"        # 日志文件保留天数
```

### 日志轮转
- **文件大小限制**: 20MB 自动轮转
- **保留策略**: 30天自动清理
- **压缩存储**: 旧日志文件自动压缩

## 📋 关键日志记录点

### 1. **企业微信消息处理**
```typescript
// 消息开始处理
this.fileLogger.logBusiness('开始处理企业微信消息', {
  messageId,
  messageType,
  userId,
  kfId,
}, 'MessageProcessor');

// 处理完成
this.fileLogger.logBusiness('企业微信消息处理完成', {
  messageId,
  processedContentLength: content.length,
}, 'MessageProcessor');
```

### 2. **AI 分析结果**
```typescript
// AI 分析成功
this.fileLogger.logBusiness('AI分析完成', {
  messageId,
  amount: result.amount,
  category: result.category,
  confidence: result.confidence,
}, 'AiService');

// AI 分析失败
this.fileLogger.error('AI分析失败', error.stack, 'AiService');
```

### 3. **数据同步操作**
```typescript
// 同步到 Notion
this.fileLogger.logBusiness('数据同步开始', {
  userId,
  platforms: ['notion', 'feishu'],
  billingInfo: data,
}, 'DataSyncProcessor');
```

### 4. **用户认证和授权**
```typescript
// 登录失败
this.fileLogger.logSecurity('用户登录失败', {
  userId,
  ip: request.ip,
  userAgent: request.headers['user-agent'],
}, 'AuthService');

// 权限验证失败
this.fileLogger.logSecurity('权限验证失败', {
  userId,
  resource: 'notion-integration',
  action: 'create',
}, 'AuthGuard');
```

### 5. **支付相关操作**
```typescript
// 支付成功
this.fileLogger.logBusiness('支付成功', {
  userId,
  orderId,
  amount,
  paymentMethod: 'wechat',
}, 'PaymentService');

// 支付失败
this.fileLogger.error('支付失败', error.stack, 'PaymentService');
```

## 🛡️ 安全日志

### 记录内容
- 登录失败尝试
- 权限验证失败
- 敏感操作（绑定/解绑平台）
- API 调用异常
- 数据访问异常

### 示例
```typescript
// 敏感操作记录
this.fileLogger.logSecurity('用户绑定Notion', {
  userId,
  notionWorkspaceId,
  timestamp: new Date().toISOString(),
}, 'NotionService');
```

## 📈 日志监控建议

### 1. **关键指标监控**
- 错误日志数量趋势
- 业务操作成功率
- 响应时间分布
- 用户活跃度

### 2. **告警规则**
- 错误日志超过阈值
- 关键业务流程失败
- 安全事件触发
- 系统资源异常

### 3. **日志分析工具**
- ELK Stack (Elasticsearch + Logstash + Kibana)
- Grafana + Loki
- 云原生日志服务

## 🔍 故障排查

### 日志查询示例
```bash
# 查看特定用户的操作日志
grep "userId.*user123" logs/business-*.log

# 查看错误日志
tail -f logs/error-$(date +%Y-%m-%d).log

# 查看安全相关事件
grep "security" logs/security-*.log | tail -20
```

### 常见问题排查
1. **消息处理失败**: 查看 `business` 和 `error` 日志
2. **AI 分析异常**: 检查 `AiService` 相关日志
3. **数据同步问题**: 查看 `DataSyncProcessor` 日志
4. **认证问题**: 检查 `security` 日志

## 📝 开发规范

### 1. **日志级别使用**
- `debug`: 详细的调试信息
- `info`: 一般信息和业务流程
- `warn`: 警告信息，不影响功能
- `error`: 错误信息，需要关注

### 2. **日志格式规范**
```typescript
// ✅ 好的日志格式
this.fileLogger.logBusiness('用户创建成功', {
  userId: user.id,
  email: user.email,
  createdAt: user.createdAt,
}, 'UserService');

// ❌ 避免的日志格式
this.logger.log('user created'); // 信息不足
this.logger.log(JSON.stringify(user)); // 可能包含敏感信息
```

### 3. **敏感信息处理**
- 不记录密码、token 等敏感信息
- 对用户数据进行脱敏处理
- 使用用户ID而非邮箱等个人信息

## 🚀 部署建议

### Docker 环境
```yaml
# docker-compose.yml
volumes:
  - ./logs:/app/logs  # 日志持久化
```

### 生产环境
- 配置日志轮转和清理策略
- 设置日志监控和告警
- 定期备份重要日志文件
- 考虑使用集中式日志管理系统
