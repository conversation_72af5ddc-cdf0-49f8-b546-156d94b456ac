# 1. 项目概述

“闪电记账”（FlashBookkeeping）是一个通过企业微信客服机器人，帮助用户快速、智能地记录财务信息的应用。用户可以通过发送文本、图片、语音或文件等多种形式的记账内容，系统利用多模态大语言模型（LLM）进行智能识别和结构化处理，最终将格式化的账目数据无缝同步到用户指定的 Notion 数据库中。

# 2. 功能性需求

## 2.1. 用户管理与绑定流程

### 2.1.1. 用户注册与登录
- **用户来源**：用户通过企业微信扫码添加客服机器人后，系统自动为其创建初始账户。
- **身份识别**：系统通过企业微信提供的 `ExternalUserID` 作为用户的唯一标识。

### 2.1.2. 目标应用绑定
- **绑定入口**：用户向机器人发送特定指令（如“绑定Notion”）触发绑定流程。
- **Notion 绑定**：
    - 系统引导用户完成 Notion 的 OAuth 授权流程。
    - 用户授权后，需选择一个特定的 Notion Database 作为记账目标。
    - 系统保存用户的 `access_token` 和所选 `database_id`。

- **多账户绑定**：允许同一用户绑定多个 Notion 账户，但每次记账只能选择一个作为目标。
- **解绑与切换**：用户可以随时解绑或切换当前使用的记账目标。

## 2.2. 核心记账流程

### 2.2.1. 信息接收
- **接收渠道**：通过企业微信客服机器人接收用户发送的消息。
- **消息类型**：
    - **文本**：如“今天打车花了35元”。
    - **图片**：如购物小票、支付截图。
    - **语音**：如口述“刚刚喝了杯咖啡，18块”。
    - **文件**：如 PDF 或 Excel 格式的账单。

### 2.2.2. 智能识别与处理
- **多模态大模型**：调用豆包等多模态大语言模型对接收到的信息进行识别。
- **关键信息提取**：模型需准确提取账目的核心要素，包括但不限于：
    - **日期**（默认为当天）
    - **金额**
    - **分类**（如餐饮、交通、购物）
    - **摘要/备注**
- **用户确认**：对于模型识别结果不确定的情况（如金额、分类模糊），系统应向用户发起澄清式提问，由用户确认后再进行记录。

### 2.2.3. 数据同步
- **目标写入**：将结构化后的账目数据写入用户当前绑定的 Notion Database 中。
- **格式映射**：系统需要根据目标数据库的字段（列）进行智能映射。首次同步时，可引导用户手动配置字段映射关系。
- **同步状态反馈**：记账成功或失败后，机器人需向用户发送明确的状态通知。失败时应提供原因及解决方案。

## 2.3. 套餐与订阅管理

### 2.3.1. 免费套餐
- **额度限制**：每个新用户自动享受免费套餐。
- **用量**：每月提供有限的免费记账条目（例如：30条/月）。
- **用量提醒**：当用户用量接近或达到上限时，系统应发送提醒。

### 2.3.2. 订阅套餐
- **付费升级**：用户可通过付费订阅解除用量限制。
- **套餐类型**：提供月度、季度、年度等多种订阅选项。
- **支付方式**：集成微信支付或支付宝作为支付渠道。
- **订阅管理**：用户可以查看自己的订阅状态、到期时间，并进行续费或取消订阅操作。

# 3. 非功能性需求

## 3.1. 安全性
- **数据传输**：所有数据在传输过程中必须使用 TLS/SSL 加密。
- **凭证存储**：用户的 Notion`access_token` 等敏感凭证必须加密存储在数据库中。
- **API 安全**：所有对外暴露的 API 都需要进行身份认证和权限校验，防止未授权访问。
- **依赖安全**：定期扫描项目依赖，及时修复已知的安全漏洞。

## 3.2. 数据隐私
- **用户数据隔离**：严格保证不同用户之间的数据隔离，任何用户都不能访问到他人的数据。
- **数据使用声明**：在用户首次使用时，提供清晰的数据隐私政策，明确告知用户数据将如何被收集、使用和存储。
- **数据删除权**：用户有权随时删除其在应用中的所有个人数据和绑定信息。

## 3.3. 性能与可靠性
- **响应时间**：从接收用户消息到返回记账结果的端到端平均响应时间应控制在 3 秒以内。
- **高可用性**：系统应具备高可用性，确保 99.9% 的时间服务可用。通过 Docker 容器化和负载均衡实现。
- **可扩展性**：系统架构应具备良好的水平扩展能力，以应对未来用户量的增长。
- **异步处理**：对于调用大模型、写入数据库等耗时操作，应采用消息队列进行异步处理，避免阻塞主流程。

## 3.4. 用户体验
- **交互友好**：机器人的交互流程应自然、简单，引导清晰。
- **错误处理**：提供友好的错误提示，并引导用户解决问题。
- **文档支持**：提供清晰的使用指南和常见问题解答（FAQ）。