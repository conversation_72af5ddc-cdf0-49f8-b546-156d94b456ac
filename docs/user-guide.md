# FlashBookkeeping 用户使用指南

## 概述

FlashBookkeeping（闪电记账）是一个基于企业微信的智能记账应用，通过AI技术自动识别和处理您的记账信息，并将数据同步到Notion第三方平台。

## 快速开始

### 1. 添加企业微信机器人

1. 扫描二维码或搜索企业微信号
2. 添加"闪电记账"机器人为好友
3. 发送任意消息开始使用

### 2. 绑定第三方平台

在开始记账前，您需要绑定至少一个第三方平台来存储记账数据。

#### 绑定Notion

**前提条件**：
- 您需要有Notion账户
- 在Notion中创建一个数据库用于存储记账数据
- 确保数据库包含以下字段：描述（标题）、金额（数字）、分类（选择）、日期（日期）

**绑定步骤**：
1. 向机器人发送：`绑定Notion`
2. 点击返回的授权链接
3. 在Notion授权页面中：
   - 选择要授权的工作区
   - 点击"允许访问"
4. 在数据库选择页面中：
   - 从下拉列表中选择要同步的数据库
   - 点击"完成绑定"
5. 看到成功页面即表示绑定完成

**数据库字段要求**：
- **描述**（Title类型）：必需，用于存储消费描述
- **金额**（Number类型）：推荐，字段名包含"金额"、"amount"等关键词
- **分类**（Select类型）：推荐，字段名包含"分类"、"category"等关键词
- **日期**（Date类型）：推荐，字段名包含"日期"、"date"等关键词

### 3. 开始记账

绑定完成后，您就可以开始记账了！

## 记账方式

### 文本记账

直接发送包含消费信息的文本消息：

**示例**：
- `午餐 25元 麦当劳`
- `地铁 3元`
- `买书 68元 《深入理解计算机系统》`
- `电影票 45元 复仇者联盟`

**AI识别规则**：
- **金额**：自动识别数字和货币符号
- **类别**：根据关键词自动分类（餐饮、交通、购物、娱乐等）
- **日期**：默认为当天，也可指定具体日期
- **描述**：提取消费场所或商品信息

### 图片记账（开发中）

上传收据、小票或发票照片，AI将自动识别其中的消费信息。

### 语音记账（开发中）

发送语音消息描述消费情况，AI将转换为文字并提取记账信息。

## 支持的消费类别

系统支持以下消费类别，AI会根据关键词自动分类：

- **餐饮**：吃饭、喝咖啡、外卖、聚餐等
- **交通**：打车、地铁、公交、加油、停车等
- **购物**：买衣服、日用品、电子产品、书籍等
- **娱乐**：电影、游戏、旅游、健身等
- **住房**：房租、水电费、物业费、装修等
- **医疗**：看病、买药、体检、保险等
- **教育**：学费、培训、课程、考试等
- **其他**：无法归类的支出

## 常用指令

### 绑定管理

- `绑定Notion` - 绑定Notion数据库
- `绑定状态` - 查看当前绑定状态

### 使用统计

- `用量` - 查看当前月使用情况
- `统计` - 查看详细使用统计

### 帮助信息

- `帮助` - 显示使用指南
- `指令` - 显示所有可用指令

## 使用限制

### 免费用户

- 每月30次记账额度
- 支持文本记账
- 基础AI识别功能

### 付费用户

- **月度订阅**：每月1000次记账额度
- **年度订阅**：每月1200次记账额度
- 支持所有记账方式
- 高级AI识别功能
- 优先客服支持

## 数据同步

### Notion同步

记账数据将自动同步到您绑定的Notion数据库中，包含以下字段：

- **描述**（标题字段）
- **金额**（数字字段）
- **分类**（选择字段）
- **日期**（日期字段）


## 常见问题

### Q: AI识别不准确怎么办？

A: 您可以：
1. 提供更详细的描述信息
2. 使用标准格式：`描述 金额 备注`
3. 手动在同步的平台中修正数据

### Q: 如何更换绑定的数据库？

A: 重新发送绑定指令，选择新的数据库即可，旧的绑定会自动失效。

### Q: 数据同步失败怎么办？

A: 检查：
1. 绑定状态是否正常
2. 第三方平台权限是否过期
3. 网络连接是否正常
4. 联系客服获取帮助

### Q: 如何查看使用额度？

A: 发送`用量`指令查看当前月使用情况和剩余额度。

### Q: 支持批量导入吗？

A: 目前暂不支持批量导入，建议逐条记录或使用第三方平台的导入功能。

### Q: 数据安全如何保障？

A: 
- 所有敏感数据都经过加密存储
- 使用HTTPS安全传输
- 定期备份数据
- 严格的访问控制

## 最佳实践

### 1. 记账格式建议

为了提高AI识别准确率，建议使用以下格式：

```
[消费场所/商品] [金额] [备注]
```

**好的示例**：
- `星巴克 28元 拿铁咖啡`
- `滴滴打车 15元 回家`
- `超市 156元 日用品采购`

**避免的格式**：
- `花了钱` （信息不完整）
- `很贵` （没有具体金额）
- `买东西28` （缺少货币单位）

### 2. 分类优化

- 使用明确的关键词帮助AI分类
- 保持描述的一致性
- 定期检查分类准确性

### 3. 数据管理

- 定期检查同步的数据
- 及时修正错误信息
- 备份重要的记账数据

## 技术支持

如果您在使用过程中遇到问题，可以：

1. 发送`帮助`查看使用指南
2. 查看常见问题解答
3. 联系客服获取技术支持

## 更新日志

### v1.0.0 (2023-07-12)

- 🎉 首次发布
- ✨ 支持文本记账
- ✨ AI智能识别
- ✨ Notion集成
- ✨ 订阅管理
- ✨ 使用统计

### 即将推出

- 📸 图片记账功能
- 🎤 语音记账功能
- 📊 数据分析报表
- 💰 预算管理功能
- 🔔 消费提醒功能
