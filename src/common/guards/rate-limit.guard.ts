import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
import { Request } from 'express';
import { Logger } from 'winston';

interface RateLimitConfig {
  limit: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: Request) => string;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

interface EndpointLimit {
  [endpoint: string]: RateLimitConfig;
}

@Injectable()
export class RateLimitGuard implements CanActivate {
  private store: RateLimitStore = {};
  private readonly defaultConfig: RateLimitConfig = {
    limit: 100,
    windowMs: 60 * 1000,
  };

  // 端点特定的限制配置
  private readonly endpointLimits: EndpointLimit = {
    '/api/wecom/callback': {
      limit: 60, // 企业微信回调每分钟60次
      windowMs: 60 * 1000,
    },
    '/api/notion/oauth': {
      limit: 10, // OAuth流程每分钟10次
      windowMs: 60 * 1000,
    },
    '/api/ai/extract': {
      limit: 30, // AI提取每分钟30次
      windowMs: 60 * 1000,
    },
  };

  constructor(@Inject('winston') private readonly logger: Logger) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const endpoint = request.route?.path || request.url;
    const config = this.endpointLimits[endpoint] || this.defaultConfig;

    // 使用用户ID或IP地址作为限流键
    const key = this.getKey(request, config);
    const now = Date.now();

    // 清理过期的记录
    this.cleanupExpiredRecords(now);

    // 初始化或更新计数
    if (!this.store[key]) {
      this.store[key] = {
        count: 1,
        resetTime: now + config.windowMs,
      };
    } else {
      this.store[key].count++;
    }

    // 检查是否超过限制
    if (this.store[key].count > config.limit) {
      this.logger.warn(`Rate limit exceeded`, {
        key,
        endpoint,
        count: this.store[key].count,
        limit: config.limit,
        ip: request.ip,
        userAgent: request.get('User-Agent'),
      });

      const exception = new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: '请求过于频繁，请稍后再试',
          error: 'Rate Limit Exceeded',
          retryAfter: Math.ceil((this.store[key].resetTime - now) / 1000),
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );

      // 设置响应头
      const response = context.switchToHttp().getResponse();
      response.set(
        'Retry-After',
        Math.ceil((this.store[key].resetTime - now) / 1000).toString(),
      );

      throw exception;
    }

    return true;
  }

  private getKey(request: Request, config: RateLimitConfig): string {
    // 优先使用用户ID，其次使用IP地址
    if (config.keyGenerator) {
      return config.keyGenerator(request);
    }

    const userId = (request as any).user?.id;
    if (userId) {
      return `user:${userId}`;
    }

    return `ip:${request.ip || request.connection.remoteAddress || 'unknown'}`;
  }

  private cleanupExpiredRecords(now: number): void {
    Object.keys(this.store).forEach((key) => {
      if (now > this.store[key].resetTime) {
        delete this.store[key];
      }
    });
  }

  // 获取当前限流状态（用于调试和监控）
  getRateLimitStatus(
    key: string,
  ): { count: number; resetTime: number; remaining: number } | null {
    const now = Date.now();
    if (this.store[key] && now <= this.store[key].resetTime) {
      return {
        count: this.store[key].count,
        resetTime: this.store[key].resetTime,
        remaining: Math.max(
          0,
          this.defaultConfig.limit - this.store[key].count,
        ),
      };
    }
    return null;
  }
}
