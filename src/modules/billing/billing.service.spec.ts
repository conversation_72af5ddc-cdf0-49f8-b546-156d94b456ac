import { Test, TestingModule } from '@nestjs/testing';
import { BillingService } from './billing.service';
import { AiService, MessageType } from '../ai/ai.service';
import { NotionService } from '../notion/notion.service';

import { UsageService } from '../usage/usage.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { WecomService } from '../wecom/wecom.service';
import { UserService } from '../user/user.service';
import { PrismaService } from '../../prisma/prisma.service';

describe('BillingService', () => {
  let service: BillingService;
  let aiService: jest.Mocked<AiService>;
  let usageService: jest.Mocked<UsageService>;
  let subscriptionService: jest.Mocked<SubscriptionService>;
  let wecomService: jest.Mocked<WecomService>;
  let userService: jest.Mocked<UserService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BillingService,
        {
          provide: AiService,
          useValue: {
            extractBillingInfo: jest.fn(),
            validateBillingInfo: jest.fn(),
          },
        },
        {
          provide: NotionService,
          useValue: {
            syncBillingData: jest.fn(),
          },
        },

        {
          provide: UsageService,
          useValue: {
            isOverLimit: jest.fn(),
            incrementUsage: jest.fn(),
          },
        },
        {
          provide: SubscriptionService,
          useValue: {
            hasValidSubscription: jest.fn(),
          },
        },
        {
          provide: WecomService,
          useValue: {
            sendMessage: jest.fn(),
          },
        },
        {
          provide: UserService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: PrismaService,
          useValue: {
            binding: {
              findMany: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<BillingService>(BillingService);
    aiService = module.get(AiService);
    usageService = module.get(UsageService);
    subscriptionService = module.get(SubscriptionService);
    wecomService = module.get(WecomService);
    userService = module.get(UserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processBilling', () => {
    const mockUser = { id: 'user1', wecom_user_id: 'wecom1' };
    const mockMessageContent = {
      type: MessageType.TEXT,
      content: '午餐 25元 麦当劳',
    };

    it('should process billing successfully', async () => {
      // Arrange
      userService.findOne.mockResolvedValue(mockUser as any);
      usageService.isOverLimit.mockResolvedValue(false);
      aiService.extractBillingInfo.mockResolvedValue({
        amount: 25,
        category: '餐饮',
        date: '2023-07-12',
        description: '午餐 麦当劳',
        confidence: 0.9,
      });
      aiService.validateBillingInfo.mockResolvedValue(true);

      // Act
      const result = await service.processBilling('user1', mockMessageContent);

      // Assert
      expect(result.success).toBe(true);
      expect(result.billingInfo).toBeDefined();
      expect(usageService.incrementUsage).toHaveBeenCalledWith('user1');
    });

    it('should fail when user not found', async () => {
      // Arrange
      userService.findOne.mockResolvedValue(null);

      // Act
      const result = await service.processBilling('user1', mockMessageContent);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('USER_NOT_FOUND');
    });

    it('should fail when usage limit exceeded', async () => {
      // Arrange
      userService.findOne.mockResolvedValue(mockUser as any);
      usageService.isOverLimit.mockResolvedValue(true);
      subscriptionService.hasValidSubscription.mockResolvedValue(false);

      // Act
      const result = await service.processBilling('user1', mockMessageContent);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('USAGE_LIMIT_EXCEEDED');
      expect(wecomService.sendMessage).toHaveBeenCalled();
    });

    it('should fail when AI processing fails', async () => {
      // Arrange
      userService.findOne.mockResolvedValue(mockUser as any);
      usageService.isOverLimit.mockResolvedValue(false);
      aiService.extractBillingInfo.mockResolvedValue({
        amount: 0,
        category: '',
        date: '',
        description: '',
        error: 'AI processing failed',
      });

      // Act
      const result = await service.processBilling('user1', mockMessageContent);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('AI_PROCESSING_FAILED');
    });
  });

  describe('handleBindingCommand', () => {
    it('should handle notion binding command', async () => {
      const result = await service.handleBindingCommand('user1', '绑定notion');
      expect(result).toContain('Notion');
    });



    it('should return null for non-binding commands', async () => {
      const result = await service.handleBindingCommand('user1', '午餐 25元');
      expect(result).toBeNull();
    });
  });
});
