import { Injectable, Logger } from '@nestjs/common';
import { AiService, MessageContent, BillingInfo } from '../ai/ai.service';
import { NotionService } from '../notion/notion.service';

import { UsageService } from '../usage/usage.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { WecomService } from '../wecom/wecom.service';
import { UserService } from '../user/user.service';
import { PrismaService } from '../../prisma/prisma.service';

export interface BillingResult {
  success: boolean;
  message: string;
  billingInfo?: BillingInfo;
  error?: string;
}

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly notionService: NotionService,

    private readonly usageService: UsageService,
    private readonly subscriptionService: SubscriptionService,
    private readonly wecomService: WecomService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * 处理记账请求的主要入口
   */
  async processBilling(
    userId: string,
    messageContent: MessageContent,
  ): Promise<BillingResult> {
    try {
      // 1. 检查用户是否存在
      const user = await this.userService.findOne(userId);
      if (!user) {
        return {
          success: false,
          message: '用户不存在',
          error: 'USER_NOT_FOUND',
        };
      }

      // 2. 检查用户使用限额
      const isOverLimit = await this.usageService.isOverLimit(userId);
      if (isOverLimit) {
        const hasValidSubscription =
          await this.subscriptionService.hasValidSubscription(userId);
        if (!hasValidSubscription) {
          // TODO: 发送消息需要kfId参数
          // await this.wecomService.sendKfMessage(kfId, userId, '您的免费使用额度已用完，请升级订阅以继续使用记账功能。');
          return {
            success: false,
            message: '使用额度已用完',
            error: 'USAGE_LIMIT_EXCEEDED',
          };
        }
      }

      // 3. 使用AI服务提取账单信息
      const billingInfo =
        await this.aiService.extractBillingInfo(messageContent);

      if (billingInfo.error) {
        // TODO: 发送消息需要kfId参数
        // await this.wecomService.sendKfMessage(kfId, userId, `处理失败: ${billingInfo.error}`);
        return {
          success: false,
          message: billingInfo.error,
          error: 'AI_PROCESSING_FAILED',
        };
      }

      // 4. 验证账单信息
      const isValid = await this.aiService.validateBillingInfo(billingInfo);
      if (!isValid) {
        // TODO: 发送消息需要kfId参数
        // await this.wecomService.sendKfMessage(kfId, userId, '提取的账单信息不完整，请重新发送或提供更详细的信息。');
        return {
          success: false,
          message: '账单信息不完整',
          error: 'INVALID_BILLING_INFO',
        };
      }

      // 5. 同步到绑定的平台
      const syncResults = await this.syncToAllBindings(userId, billingInfo);

      // 6. 增加使用计数
      await this.usageService.incrementUsage(userId);

      // 7. 发送成功消息
      const successMessage = this.formatSuccessMessage(
        billingInfo,
        syncResults,
      );
      // TODO: 发送消息需要kfId参数
      // await this.wecomService.sendKfMessage(kfId, userId, successMessage);

      return {
        success: true,
        message: '记账成功',
        billingInfo,
      };
    } catch (error) {
      this.logger.error('处理记账请求失败:', error);
      // TODO: 发送消息需要kfId参数
      // await this.wecomService.sendKfMessage(kfId, userId, '处理记账时发生错误，请稍后重试。');

      return {
        success: false,
        message: '系统错误',
        error: 'SYSTEM_ERROR',
      };
    }
  }

  /**
   * 同步到所有绑定的平台
   */
  private async syncToAllBindings(userId: string, billingInfo: BillingInfo) {
    const results = {
      notion: { success: false, error: null as string | null },
    };

    // 获取用户的所有活跃绑定
    const bindings = await this.prisma.binding.findMany({
      where: {
        user_id: userId,
        is_active: true,
      },
    });

    // 同步到Notion
    const notionBindings = bindings.filter((b) => b.type === 'NOTION');
    if (notionBindings.length > 0) {
      try {
        await this.notionService.syncBillingData(userId, billingInfo);
        results.notion.success = true;
        this.logger.log(`成功同步到Notion: ${userId}`);
      } catch (error) {
        results.notion.error = error.message;
        this.logger.error(`同步到Notion失败: ${userId}`, error);
      }
    }



    return results;
  }

  /**
   * 格式化成功消息
   */
  private formatSuccessMessage(
    billingInfo: BillingInfo,
    syncResults: any,
  ): string {
    let message = `✅ 记账成功！\n\n`;
    message += `💰 金额: ¥${billingInfo.amount}\n`;
    message += `📂 类别: ${billingInfo.category}\n`;
    message += `📅 日期: ${billingInfo.date}\n`;
    message += `📝 描述: ${billingInfo.description}\n`;

    message += `\n📊 同步状态:\n`;

    if (syncResults.notion.success) {
      message += `✅ Notion: 同步成功\n`;
    } else if (syncResults.notion.error) {
      message += `❌ Notion: ${syncResults.notion.error}\n`;
    }

    if (!syncResults.notion.success) {
      message += `⚠️ 未绑定任何平台，请先绑定Notion账户`;
    }

    return message;
  }

  /**
   * 获取用户绑定状态
   */
  async getUserBindingStatus(userId: string) {
    const bindings = await this.prisma.binding.findMany({
      where: {
        user_id: userId,
        is_active: true,
      },
    });

    return {
      hasNotion: bindings.some((b) => b.type === 'NOTION'),
      totalBindings: bindings.length,
      bindings: bindings.map((b) => ({
        type: b.type,
        targetName: b.target_name,
        createdAt: b.created_at,
      })),
    };
  }

  /**
   * 处理绑定指令
   */
  async handleBindingCommand(
    userId: string,
    command: string,
  ): Promise<string | null> {
    const lowerCommand = command.toLowerCase().trim();

    if (lowerCommand === '绑定notion' || lowerCommand === 'bind notion') {
      const authUrl = this.notionService.getAuthUrl(userId);
      return `请点击以下链接完成Notion绑定：\n${authUrl}`;
    }

    if (lowerCommand === '绑定状态' || lowerCommand === 'binding status') {
      const status = await this.getUserBindingStatus(userId);
      let message = `📊 绑定状态:\n\n`;

      if (status.totalBindings === 0) {
        message += `❌ 未绑定任何平台\n\n`;
        message += `请发送以下指令进行绑定:\n`;
        message += `• "绑定Notion" - 绑定Notion数据库`;
      } else {
        message += `✅ 已绑定 ${status.totalBindings} 个平台:\n\n`;
        status.bindings.forEach((binding) => {
          message += `• ${binding.type}: ${binding.targetName || '未命名'}\n`;
        });
      }

      return message;
    }

    if (lowerCommand === '帮助' || lowerCommand === 'help') {
      return this.getHelpMessage();
    }

    return null; // 不是绑定指令
  }

  /**
   * 获取帮助信息
   */
  private getHelpMessage(): string {
    return `📖 闪电记账使用指南

🔗 绑定指令:
• "绑定Notion" - 绑定Notion数据库
• "绑定状态" - 查看当前绑定状态

💰 记账方式:
• 发送文本: "午餐 25元 麦当劳"
• 发送图片: 上传收据或小票照片
• 发送语音: 语音描述消费情况

📊 其他指令:
• "帮助" - 显示此帮助信息
• "用量" - 查看当前使用情况

💡 提示: 记账信息会自动识别金额、类别、日期等信息，并同步到您绑定的平台中。`;
  }
}
