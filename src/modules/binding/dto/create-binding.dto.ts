import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsBoolean,
} from 'class-validator';

/**
 * 绑定类型枚举
 */
export enum BindingType {
  NOTION = 'NOTION',
}

/**
 * 创建第三方账号绑定关系的DTO
 */
export class CreateBindingDto {
  /**
   * 绑定类型 - 枚举值: NOTION(Notion)
   */
  @IsEnum(BindingType)
  type: BindingType;

  /**
   * 第三方平台的访问令牌
   */
  @IsString()
  @IsNotEmpty()
  access_token: string;

  /**
   * 第三方平台的用户/应用ID
   */
  @IsString()
  @IsNotEmpty()
  target_id: string;

  /**
   * 第三方平台的用户/应用名称(可选)
   */
  @IsString()
  @IsOptional()
  target_name?: string;

  /**
   * 是否激活该绑定(默认false)
   */
  @IsBoolean()
  @IsOptional()
  is_active?: boolean = false;
}
