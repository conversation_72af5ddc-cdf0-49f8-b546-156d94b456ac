-- 插入测试用户
INSERT INTO users (id, external_id, name, created_at, updated_at) 
VALUES ('test_bound_user_456', 'test_bound_user_456', '测试已绑定用户', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 插入测试绑定记录
INSERT INTO bindings (id, user_id, type, access_token, target_id, target_name, is_active, created_at, updated_at)
VALUES (
  'test_binding_123',
  'test_bound_user_456',
  'NOTION',
  'encrypted_test_token',
  'test_database_id_789',
  '测试记账数据库',
  true,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;
