// 简单的测试脚本来验证进入会话逻辑
const axios = require('axios');

async function testEnterSession() {
  const baseUrl = 'http://localhost:3000';
  
  // 测试用户ID
  const testUserId = 'test_user_123';
  const testKfId = 'test_kf_456';
  
  try {
    console.log('🧪 开始测试进入会话功能...');
    
    // 1. 首先检查用户是否已绑定Notion
    console.log('\n1. 检查用户Notion绑定状态...');
    try {
      const bindingResponse = await axios.get(`${baseUrl}/notion/binding/${testUserId}`);
      console.log('绑定状态:', bindingResponse.data);
    } catch (error) {
      console.log('检查绑定状态失败:', error.response?.data || error.message);
    }
    
    // 2. 测试进入会话事件
    console.log('\n2. 测试进入会话事件...');
    try {
      const enterSessionResponse = await axios.post(`${baseUrl}/wecom/test-enter-session`, {
        userId: testUserId,
        kfId: testKfId
      });
      console.log('进入会话测试结果:', enterSessionResponse.data);
    } catch (error) {
      console.log('进入会话测试失败:', error.response?.data || error.message);
    }
    
    console.log('\n✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testEnterSession();
